<?php

use Illuminate\Support\Facades\Route;
use App\Models\Article;

Route::get('/', function () {
    // Get the latest 4 published cases for the home page showcase
    $cases = App\Models\PortfolioCase::where('is_published', true)
        ->with('portfolioTag')
        ->latest()
        ->take(4)
        ->get()
        ->map(function ($case) {
            return [
                'title' => $case->title,
                'img' => $case->thumbnail ?: ($case->images[0] ?? '/images/placeholder.jpg'),
                'tag' => $case->portfolioTag ? $case->portfolioTag->name : null,
                'url' => route('case.detail', $case->slug),
            ];
        });

    $seoData = seo_page_data([
        'title' => 'Panda Patronage - Digital Marketing & Web Development Agency',
        'description' => 'Transform your digital presence with Panda Patronage. Expert web development, digital marketing, and creative solutions to grow your business online.',
        'keywords' => 'web development, digital marketing, Laravel, React, SEO, social media marketing, web design, Montreal agency',
        'og:image' => url('/images/misc/og-home.jpg'),
        'og:type' => 'website',
    ], [
        'organization' => seo_structured_data('Organization'),
        'website' => seo_structured_data('WebSite'),
    ]);

    return inertia('Home', compact('seoData', 'cases'));
})->name('home');

Route::get('/cases', function () {
    $cases = App\Models\PortfolioCase::where('is_published', true)
        ->with('portfolioTag')
        ->latest()
        ->get()
        ->map(function ($case) {
            return [
                'title' => $case->title,
                'img' => $case->thumbnail ?: ($case->images[0] ?? '/images/placeholder.jpg'),
                'tag' => $case->portfolioTag ? $case->portfolioTag->name : null,
                'url' => route('case.detail', $case->slug),
            ];
        });

    $seoData = seo_page_data([
        'title' => 'Our Portfolio & Case Studies',
        'description' => 'Explore our portfolio of successful web development and digital marketing projects. See how we\'ve helped businesses grow their online presence.',
        'keywords' => 'portfolio, case studies, web development projects, digital marketing campaigns, client success stories',
        'og:image' => url('/images/misc/og-cases.jpg'),
        'og:type' => 'website',
    ], [
        'breadcrumbs' => seo_breadcrumbs([
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'Cases', 'url' => route('cases')],
        ]),
    ]);

    return inertia('Cases', compact('cases', 'seoData'));
})->name('cases');

Route::get('/cases/{slug}', function ($slug) {
    $case = App\Models\PortfolioCase::where('slug', $slug)->where('is_published', true)->first();

    if (!$case) {
        abort(404);
    }

    $caseDetail = [
        'logo' => $case->logo ?: '/images/placeholder-logo.png',
        'projectName' => $case->title,
        'companyName' => $case->company_name,
        'images' => $case->images ?: [],
    ];

    // Get other published cases for showcase
    $cases = App\Models\PortfolioCase::where('is_published', true)
        ->where('id', '!=', $case->id)
        ->with('portfolioTag')
        ->latest()
        ->take(12)
        ->get()
        ->map(function ($otherCase) {
            return [
                'title' => $otherCase->title,
                'img' => $otherCase->thumbnail ?: ($otherCase->images[0] ?? '/images/placeholder.jpg'),
                'tag' => $otherCase->portfolioTag ? $otherCase->portfolioTag->name : null,
                'url' => route('case.detail', $otherCase->slug),
            ];
        });
    $seoData = seo_page_data([
        'title' => $caseDetail['projectName'],
        'description' => 'Discover the project ' . $caseDetail['projectName'] . ' by ' . $caseDetail['companyName'] . ' – a showcase of creative branding and design.',
        'keywords' => 'branding, design, portfolio, ' . $caseDetail['projectName'] . ', ' . $caseDetail['companyName'],
        'og:image' => $caseDetail['images'][1],
        'og:type' => 'website',
    ], [
        'organization' => seo_structured_data('Organization'),
        'breadcrumbs' => seo_breadcrumbs([
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'Cases', 'url' => route('cases')],
            ['name' => $caseDetail['projectName'], 'url' => route('case.detail', $slug)],
        ]),
    ]);
    return inertia('CaseDetail', [
        'caseDetail' => $caseDetail,
        'cases' => $cases,
        'seoData' => $seoData,
    ]);
})->name('case.detail');

Route::get('/about', function () {
    $seoData = seo_page_data([
        'title' => 'About Us - Meet the Panda Patronage Team',
        'description' => 'Learn about Panda Patronage, our mission, values, and the talented team behind our digital marketing and web development services.',
        'keywords' => 'about us, team, company, digital agency, web development team, Montreal, mission, values',
        'og:image' => url('/images/misc/og-about.jpg'),
        'og:type' => 'website',
    ], [
        'breadcrumbs' => seo_breadcrumbs([
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'About', 'url' => route('about')],
        ]),
    ]);

    return inertia('About', compact('seoData'));
})->name('about');

Route::get('/blog', function () {
    $articles = Article::with('blogTag')->get();

    $seoData = seo_page_data([
        'title' => 'Blog - Digital Marketing & Web Development Insights',
        'description' => 'Stay updated with the latest trends in web development, digital marketing, and technology. Expert insights and tips from Panda Patronage.',
        'keywords' => 'blog, digital marketing blog, web development articles, SEO tips, technology insights, tutorials',
        'og:image' => url('/images/misc/og-blog.jpg'),
        'og:type' => 'website',
    ], [
        'breadcrumbs' => seo_breadcrumbs([
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'Blog', 'url' => route('blog')],
        ]),
    ]);

    return inertia('Blogs', compact('articles', 'seoData'));
})->name('blog');

Route::get('/contact', function () {
    $seoData = seo_page_data([
        'title' => 'Contact Us - Get in Touch with Panda Patronage',
        'description' => 'Ready to transform your digital presence? Contact Panda Patronage for web development, digital marketing, and creative solutions. Located in Montreal, Canada.',
        'keywords' => 'contact, get in touch, Montreal web development, digital marketing agency, consultation, quote',
        'og:image' => url('/images/misc/og-contact.jpg'),
        'og:type' => 'website',
    ], [
        'organization' => seo_structured_data('Organization'),
        'breadcrumbs' => seo_breadcrumbs([
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'Contact', 'url' => route('contact')],
        ]),
    ]);

    return inertia('Contact', compact('seoData'));
})->name('contact');

Route::get('/privacy-policy', function () {
    return inertia('PrivacyPolicy');
})->name('privacy-policy');

Route::get('/licensing', function () {
    return inertia('Licensing');
})->name('licensing');

Route::get('/terms-of-use', function () {
    return inertia('TermsOfUse');
})->name('terms-of-use');

Route::get('/blog/{slug}', function ($slug) {
    $article = Article::with('blogTag')->where('slug', $slug)->first();

    if (!$article) {
        abort(404);
    }

    $tagName = $article->blogTag ? $article->blogTag->name : 'Blog';

    $seoData = seo_page_data([
        'title' => $article->title,
        'description' => $article->content,
        'keywords' => $tagName . ', blog, digital marketing, web development',
        'og:image' => url($article->image),
        'og:type' => 'article',
        'og:article:published_time' => $article->created_at->toISOString(),
        'og:article:modified_time' => $article->updated_at->toISOString(),
        'og:article:author' => 'Panda Patronage',
        'og:article:section' => $tagName,
    ], [
        'article' => seo_structured_data('Article', [
            'headline' => $article->title,
            'description' => $article->excerpt,
            'image' => url($article->image),
            'datePublished' => $article->created_at->toISOString(),
            'dateModified' => $article->updated_at->toISOString(),
            'articleSection' => $tagName,
        ]),
        'breadcrumbs' => seo_breadcrumbs([
            ['name' => 'Home', 'url' => route('home')],
            ['name' => 'Blog', 'url' => route('blog')],
            ['name' => $article->title, 'url' => route('blog.detail', $article->slug)],
        ]),
    ]);

    return inertia('BlogDetailDynamic', compact('article', 'seoData'));
})->name('blog.detail');

// SEO Routes
Route::get('/sitemap.xml', function () {
    $sitemap = file_get_contents(public_path('sitemap.xml'));
    return response($sitemap, 200, [
        'Content-Type' => 'application/xml',
        'Cache-Control' => 'public, max-age=3600',
    ]);
})->name('sitemap');

Route::get('/robots.txt', function () {
    $robots = file_get_contents(public_path('robots.txt'));
    return response($robots, 200, [
        'Content-Type' => 'text/plain',
        'Cache-Control' => 'public, max-age=86400',
    ]);
})->name('robots');

// Admin Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Guest admin routes (login)
    Route::middleware('admin.guest')->group(function () {
        Route::get('/login', [App\Http\Controllers\Admin\AuthController::class, 'showLogin'])->name('login');
        Route::post('/login', [App\Http\Controllers\Admin\AuthController::class, 'login']);
    });

    // Protected admin routes
    Route::middleware('admin')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
        Route::post('/logout', [App\Http\Controllers\Admin\AuthController::class, 'logout'])->name('logout');

        // Blog management
        Route::resource('articles', App\Http\Controllers\Admin\ArticleController::class);
        Route::resource('blog-tags', App\Http\Controllers\Admin\BlogTagController::class);

        // Cases management
        Route::resource('cases', App\Http\Controllers\Admin\CaseController::class);
        Route::resource('portfolio-tags', App\Http\Controllers\Admin\PortfolioTagController::class);

        // Contact queries management
        Route::get('contact-queries', [App\Http\Controllers\Admin\ContactQueryController::class, 'index'])->name('contact-queries.index');
        Route::get('contact-queries/export', [App\Http\Controllers\Admin\ContactQueryController::class, 'export'])->name('contact-queries.export');
        Route::delete('contact-queries/{contactQuery}', [App\Http\Controllers\Admin\ContactQueryController::class, 'destroy'])->name('contact-queries.destroy');
        Route::delete('contact-queries', [App\Http\Controllers\Admin\ContactQueryController::class, 'bulkDestroy'])->name('contact-queries.bulk-destroy');

        // Profile management
        Route::get('profile/password', [App\Http\Controllers\Admin\ProfileController::class, 'showPasswordForm'])->name('profile.password');
        Route::post('profile/password', [App\Http\Controllers\Admin\ProfileController::class, 'updatePassword'])->name('profile.password.update');
    });
});

// Contact form submission (public)
Route::post('/contact', [App\Http\Controllers\ContactController::class, 'store'])->name('contact.store');


